{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16440026094182412302, "build_script_build", false, 3779294988916283669], [10755362358622467486, "build_script_build", false, 5071385289108831154], [17218623086136245857, "build_script_build", false, 16063369733645975379]], "local": [{"RerunIfChanged": {"output": "debug\\build\\sftp-web-6adf51d427496c07\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}