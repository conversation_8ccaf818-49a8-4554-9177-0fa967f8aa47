{"rustc": 10895048813736897673, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 17913617818440560221, "deps": [[1615478164327904835, "pin_utils", false, 9651642200097773439], [1906322745568073236, "pin_project_lite", false, 11822659073999105922], [5451793922601807560, "slab", false, 2095291213667454362], [7620660491849607393, "futures_core", false, 2673114527437137714], [10565019901765856648, "futures_macro", false, 9659185758245546792], [16240732885093539806, "futures_task", false, 10839731063925382365]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-b67e10d0f96b8276\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}