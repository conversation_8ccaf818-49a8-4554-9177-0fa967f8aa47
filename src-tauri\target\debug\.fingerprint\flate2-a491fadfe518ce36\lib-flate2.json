{"rustc": 10895048813736897673, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2225463790103693989, "path": 6609354086612049105, "deps": [[5466618496199522463, "crc32fast", false, 3577884901820611307], [7636735136738807108, "miniz_oxide", false, 2527077014224110259]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-a491fadfe518ce36\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}