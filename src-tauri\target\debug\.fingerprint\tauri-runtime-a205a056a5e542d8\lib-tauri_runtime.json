{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 17993515530118038896, "deps": [[442785307232013896, "build_script_build", false, 10049441755064117178], [3150220818285335163, "url", false, 3657986470946840440], [4143744114649553716, "raw_window_handle", false, 16868371766132806453], [7606335748176206944, "dpi", false, 11382569815231692679], [9010263965687315507, "http", false, 13707547862268365133], [9689903380558560274, "serde", false, 99868426957841695], [10806645703491011684, "thiserror", false, 6103424829622831816], [11050281405049894993, "tauri_utils", false, 13787090986115663563], [14585479307175734061, "windows", false, 5230279177150286504], [15367738274754116744, "serde_json", false, 17312796300219690927], [16727543399706004146, "cookie", false, 2771977539569502764]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-a205a056a5e542d8\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}