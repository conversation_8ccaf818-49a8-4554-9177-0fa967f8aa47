{"rustc": 10895048813736897673, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 845356035088538482, "deps": [[3150220818285335163, "url", false, 12655493810620714798], [6913375703034175521, "build_script_build", false, 5787513785174519605], [8319709847752024821, "uuid1", false, 11339950564336125742], [9122563107207267705, "dyn_clone", false, 5624174627330178513], [9689903380558560274, "serde", false, 6763933132116834322], [14923790796823607459, "indexmap", false, 16537106912287068141], [15367738274754116744, "serde_json", false, 11459032808059228803], [16071897500792579091, "schemars_derive", false, 13746346015231671754]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-4ead32f19daa24d5\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}