{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16440026094182412302, "build_script_build", false, 4078440559280399452], [10755362358622467486, "build_script_build", false, 11254940658638353063], [17218623086136245857, "build_script_build", false, 749123025295404680]], "local": [{"RerunIfChanged": {"output": "debug\\build\\sftp-web-dc366399b57464f4\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}