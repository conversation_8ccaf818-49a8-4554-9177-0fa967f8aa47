{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 3854729686355239864, "deps": [[4899080583175475170, "semver", false, 8371616595847207021], [6913375703034175521, "schemars", false, 10232491167744959992], [7170110829644101142, "json_patch", false, 14462239644424083236], [9689903380558560274, "serde", false, 6763933132116834322], [11050281405049894993, "tauri_utils", false, 1252227277618345100], [12714016054753183456, "tauri_winres", false, 17984853270485169179], [13077543566650298139, "heck", false, 16773306763149267354], [13475171727366188400, "cargo_toml", false, 14934897555132121675], [13625485746686963219, "anyhow", false, 17145296041573005949], [15367738274754116744, "serde_json", false, 11459032808059228803], [15609422047640926750, "toml", false, 12405456715105559855], [15622660310229662834, "walkdir", false, 13637727522899890893], [16928111194414003569, "dirs", false, 9158058224392114204], [17155886227862585100, "glob", false, 9625159244041247292]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-67a8c26cdc9438dd\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}