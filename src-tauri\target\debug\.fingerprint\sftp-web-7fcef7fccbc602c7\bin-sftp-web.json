{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 14054290380619006806, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[15171501230327402, "async_std", false, 13672073651456798116], [830578266177771953, "russh", false, 16665712676258037828], [2706460456408817945, "futures", false, 16467176724396014658], [3834743577069889284, "tauri_plugin_dialog", false, 1571485381343669629], [9538054652646069845, "tokio", false, 701189010124974910], [9689903380558560274, "serde", false, 17823848085034589135], [9897246384292347999, "chrono", false, 16329332326017615531], [10619844418038398281, "russh_sftp", false, 9691961545039376816], [10755362358622467486, "tauri", false, 18128225688018541081], [15367738274754116744, "serde_json", false, 10945572041705189800], [16440026094182412302, "sftp_web_lib", false, 17149557150291409143], [16440026094182412302, "build_script_build", false, 8519594635264861869], [17218623086136245857, "tauri_plugin_opener", false, 3814673116990992395]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sftp-web-7fcef7fccbc602c7\\dep-bin-sftp-web", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}