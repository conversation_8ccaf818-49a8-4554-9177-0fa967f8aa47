{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 17069650581314812224, "deps": [[40386456601120721, "percent_encoding", false, 3166459414843997730], [442785307232013896, "tauri_runtime", false, 10692310428565949225], [1200537532907108615, "url<PERSON><PERSON>n", false, 7354186198381754366], [3150220818285335163, "url", false, 3657986470946840440], [4143744114649553716, "raw_window_handle", false, 16868371766132806453], [4341921533227644514, "muda", false, 8088071707832910088], [4919829919303820331, "serialize_to_javascript", false, 3192266041874937552], [5986029879202738730, "log", false, 13388491360176584128], [7752760652095876438, "tauri_runtime_wry", false, 17116932754397395883], [8539587424388551196, "webview2_com", false, 11009233570342220424], [9010263965687315507, "http", false, 13707547862268365133], [9228235415475680086, "tauri_macros", false, 17828918088899209486], [9538054652646069845, "tokio", false, 2093541074312367227], [9689903380558560274, "serde", false, 99868426957841695], [9920160576179037441, "getrandom", false, 11914301178649928801], [10229185211513642314, "mime", false, 6935398437266411033], [10629569228670356391, "futures_util", false, 6213211003711210660], [10755362358622467486, "build_script_build", false, 11254940658638353063], [10806645703491011684, "thiserror", false, 6103424829622831816], [11050281405049894993, "tauri_utils", false, 13787090986115663563], [11989259058781683633, "dunce", false, 8993868349159878499], [12565293087094287914, "window_vibrancy", false, 11227704511595279797], [12986574360607194341, "serde_repr", false, 13703274093119033485], [13077543566650298139, "heck", false, 16773306763149267354], [13625485746686963219, "anyhow", false, 17145296041573005949], [14585479307175734061, "windows", false, 5230279177150286504], [15367738274754116744, "serde_json", false, 17312796300219690927], [16928111194414003569, "dirs", false, 9158058224392114204], [17155886227862585100, "glob", false, 9625159244041247292]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0f10bab2d2d55ccb\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}