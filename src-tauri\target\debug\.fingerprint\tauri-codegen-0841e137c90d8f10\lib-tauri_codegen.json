{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 4112020953269458598, "deps": [[3060637413840920116, "proc_macro2", false, 9898791222778704133], [3150220818285335163, "url", false, 12655493810620714798], [4899080583175475170, "semver", false, 8371616595847207021], [7170110829644101142, "json_patch", false, 14462239644424083236], [7392050791754369441, "ico", false, 17472005340697165376], [8319709847752024821, "uuid", false, 16731912184652026916], [9689903380558560274, "serde", false, 6763933132116834322], [9857275760291862238, "sha2", false, 12318357967159454636], [10640660562325816595, "syn", false, 5048777514585564967], [10806645703491011684, "thiserror", false, 6103424829622831816], [11050281405049894993, "tauri_utils", false, 16045400007545760287], [12687914511023397207, "png", false, 8361901623250955778], [13077212702700853852, "base64", false, 4144784392564059885], [14132538657330703225, "brotli", false, 13442052967841458234], [15367738274754116744, "serde_json", false, 11459032808059228803], [15622660310229662834, "walkdir", false, 13637727522899890893], [17990358020177143287, "quote", false, 13527854320717894611]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-0841e137c90d8f10\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}