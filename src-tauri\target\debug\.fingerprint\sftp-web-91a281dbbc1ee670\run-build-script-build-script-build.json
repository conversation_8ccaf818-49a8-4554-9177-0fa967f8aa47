{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16440026094182412302, "build_script_build", false, 6703314777777024475], [10755362358622467486, "build_script_build", false, 10153625634090657252], [17218623086136245857, "build_script_build", false, 9852646940842603140]], "local": [{"RerunIfChanged": {"output": "debug\\build\\sftp-web-91a281dbbc1ee670\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}