{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 13667997004821700218, "deps": [[561782849581144631, "html5ever", false, 1805973846111476424], [1200537532907108615, "url<PERSON><PERSON>n", false, 7354186198381754366], [3150220818285335163, "url", false, 3657986470946840440], [3191507132440681679, "serde_untagged", false, 13033575058246757736], [4899080583175475170, "semver", false, 17783805046897248549], [5578504951057029730, "serde_with", false, 9155302596667869005], [5986029879202738730, "log", false, 13388491360176584128], [6262254372177975231, "kuchiki", false, 11990172787520786550], [6606131838865521726, "ctor", false, 14182983749263760031], [7170110829644101142, "json_patch", false, 17332906184261191804], [8319709847752024821, "uuid", false, 12966098575811772004], [9010263965687315507, "http", false, 13707547862268365133], [9451456094439810778, "regex", false, 7881417031046486631], [9689903380558560274, "serde", false, 99868426957841695], [10806645703491011684, "thiserror", false, 6103424829622831816], [11989259058781683633, "dunce", false, 8993868349159878499], [13625485746686963219, "anyhow", false, 17145296041573005949], [14132538657330703225, "brotli", false, 13442052967841458234], [15367738274754116744, "serde_json", false, 17312796300219690927], [15609422047640926750, "toml", false, 8406504428901386982], [15622660310229662834, "walkdir", false, 4007733077681490748], [15932120279885307830, "memchr", false, 261397065016885617], [17146114186171651583, "infer", false, 6316403168397676785], [17155886227862585100, "glob", false, 9625159244041247292], [17186037756130803222, "phf", false, 3672492239073589384]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-fc761faf60366c20\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}