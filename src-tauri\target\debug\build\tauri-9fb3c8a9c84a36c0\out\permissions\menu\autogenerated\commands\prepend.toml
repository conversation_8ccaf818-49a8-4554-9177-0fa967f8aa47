# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-prepend"
description = "Enables the prepend command without any pre-configured scope."
commands.allow = ["prepend"]

[[permission]]
identifier = "deny-prepend"
description = "Denies the prepend command without any pre-configured scope."
commands.deny = ["prepend"]
