{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 4848383146185408503, "deps": [[376837177317575824, "build_script_build", false, 2499311803424564045], [4143744114649553716, "raw_window_handle", false, 16868371766132806453], [5986029879202738730, "log", false, 13388491360176584128], [10281541584571964250, "windows_sys", false, 13251489860031489772]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-db6af62dd30bc479\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}