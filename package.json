{"name": "sftp-web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "tailwindcss": "^4.1.10", "vue": "^3.5.13"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "daisyui": "^5.0.43", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}