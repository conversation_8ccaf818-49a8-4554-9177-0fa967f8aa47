["\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\path\\autogenerated\\default.toml"]