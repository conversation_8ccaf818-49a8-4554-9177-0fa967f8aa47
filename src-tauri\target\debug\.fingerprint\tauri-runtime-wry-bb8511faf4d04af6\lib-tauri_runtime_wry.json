{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 10451378034595227560, "deps": [[376837177317575824, "softbuffer", false, 1852848606481693597], [442785307232013896, "tauri_runtime", false, 11251217813857764485], [3150220818285335163, "url", false, 3657986470946840440], [3722963349756955755, "once_cell", false, 3835253594759767131], [4143744114649553716, "raw_window_handle", false, 16868371766132806453], [5986029879202738730, "log", false, 13388491360176584128], [7752760652095876438, "build_script_build", false, 7605890276426213681], [8539587424388551196, "webview2_com", false, 11009233570342220424], [9010263965687315507, "http", false, 13707547862268365133], [11050281405049894993, "tauri_utils", false, 1434155286854631486], [13223659721939363523, "tao", false, 11103631738860788432], [14585479307175734061, "windows", false, 5230279177150286504], [14794439852947137341, "wry", false, 5309955723346325094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-bb8511faf4d04af6\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}