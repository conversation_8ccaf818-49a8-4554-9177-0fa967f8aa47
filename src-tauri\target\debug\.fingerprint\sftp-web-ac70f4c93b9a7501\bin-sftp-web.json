{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 14054290380619006806, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1173344670431821891, "ssh2", false, 7237579693217607562], [2706460456408817945, "futures", false, 7633138577380883665], [8319709847752024821, "uuid", false, 8383981956913330292], [9538054652646069845, "tokio", false, 2093541074312367227], [9689903380558560274, "serde", false, 99868426957841695], [9897246384292347999, "chrono", false, 6597450425357865034], [10755362358622467486, "tauri", false, 12932140667094713432], [13625485746686963219, "anyhow", false, 17145296041573005949], [15367738274754116744, "serde_json", false, 17312796300219690927], [16440026094182412302, "sftp_web_lib", false, 1234980504585754813], [16440026094182412302, "build_script_build", false, 4789373144955760509], [17218623086136245857, "tauri_plugin_opener", false, 14061053276315497053]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sftp-web-ac70f4c93b9a7501\\dep-bin-sftp-web", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}