use std::collections::HashMap;
use std::path::Path;
use std::sync::Mutex;
use serde::{Deserialize, Serialize};
use ssh2::Session;
use std::net::TcpStream;

// SFTP 连接信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SftpConnectionInfo {
    pub id: String,
    pub name: String,
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub connected: bool,
}

// 文件信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub is_dir: bool,
    pub modified: Option<String>,
    pub permissions: String,
}

// 传输进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransferProgress {
    pub id: String,
    pub filename: String,
    pub total_size: u64,
    pub transferred: u64,
    pub speed: f64,
    pub status: String,
}

// 全局连接管理器
type ConnectionManager = Mutex<HashMap<String, Session>>;
static CONNECTIONS: std::sync::LazyLock<ConnectionManager> =
    std::sync::LazyLock::new(|| Mutex::new(HashMap::new()));

// 测试连接命令
#[tauri::command]
async fn test_sftp_connection(
    host: String,
    port: u16,
    username: String,
    password: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let tcp = TcpStream::connect(format!("{}:{}", host, port))
            .map_err(|e| format!("连接失败: {}", e))?;

        let mut session = Session::new()
            .map_err(|e| format!("创建会话失败: {}", e))?;

        session.set_tcp_stream(tcp);
        session.handshake()
            .map_err(|e| format!("握手失败: {}", e))?;

        session.userauth_password(&username, &password)
            .map_err(|e| format!("认证失败: {}", e))?;

        if !session.authenticated() {
            return Err("认证失败".to_string());
        }

        Ok("连接成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 建立 SFTP 连接
#[tauri::command]
async fn connect_sftp(
    connection_info: SftpConnectionInfo,
) -> Result<String, String> {
    let connection_id = connection_info.id.clone();

    tokio::task::spawn_blocking(move || {
        let tcp = TcpStream::connect(format!("{}:{}", connection_info.host, connection_info.port))
            .map_err(|e| format!("连接失败: {}", e))?;

        let mut session = Session::new()
            .map_err(|e| format!("创建会话失败: {}", e))?;

        session.set_tcp_stream(tcp);
        session.handshake()
            .map_err(|e| format!("握手失败: {}", e))?;

        session.userauth_password(&connection_info.username, &connection_info.password)
            .map_err(|e| format!("认证失败: {}", e))?;

        if !session.authenticated() {
            return Err("认证失败".to_string());
        }

        // 存储连接
        let mut connections = CONNECTIONS.lock().unwrap();
        connections.insert(connection_id.clone(), session);

        Ok(connection_id)
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 列出目录内容
#[tauri::command]
async fn list_directory(
    connection_id: String,
    path: String,
) -> Result<Vec<FileInfo>, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        let entries = sftp.readdir(Path::new(&path))
            .map_err(|e| format!("读取目录失败: {}", e))?;

        let mut files = Vec::new();
        for (path, stat) in entries {
            let name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("未知")
                .to_string();

            let file_info = FileInfo {
                name,
                path: path.to_string_lossy().to_string(),
                size: stat.size.unwrap_or(0),
                is_dir: stat.is_dir(),
                modified: None, // 可以后续添加时间解析
                permissions: format!("{:o}", stat.perm.unwrap_or(0)),
            };
            files.push(file_info);
        }

        Ok(files)
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 下载文件
#[tauri::command]
async fn download_file(
    connection_id: String,
    remote_path: String,
    local_path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        let mut remote_file = sftp.open(Path::new(&remote_path))
            .map_err(|e| format!("打开远程文件失败: {}", e))?;

        let mut local_file = std::fs::File::create(&local_path)
            .map_err(|e| format!("创建本地文件失败: {}", e))?;

        std::io::copy(&mut remote_file, &mut local_file)
            .map_err(|e| format!("文件传输失败: {}", e))?;

        Ok("下载完成".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 上传文件
#[tauri::command]
async fn upload_file(
    connection_id: String,
    local_path: String,
    remote_path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        let mut local_file = std::fs::File::open(&local_path)
            .map_err(|e| format!("打开本地文件失败: {}", e))?;

        let mut remote_file = sftp.create(Path::new(&remote_path))
            .map_err(|e| format!("创建远程文件失败: {}", e))?;

        std::io::copy(&mut local_file, &mut remote_file)
            .map_err(|e| format!("文件传输失败: {}", e))?;

        Ok("上传完成".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 创建目录
#[tauri::command]
async fn create_directory(
    connection_id: String,
    path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        sftp.mkdir(Path::new(&path), 0o755)
            .map_err(|e| format!("创建目录失败: {}", e))?;

        Ok("目录创建成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 删除文件或目录
#[tauri::command]
async fn delete_file(
    connection_id: String,
    path: String,
    is_dir: bool,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        if is_dir {
            sftp.rmdir(Path::new(&path))
                .map_err(|e| format!("删除目录失败: {}", e))?;
        } else {
            sftp.unlink(Path::new(&path))
                .map_err(|e| format!("删除文件失败: {}", e))?;
        }

        Ok("删除成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 断开连接
#[tauri::command]
async fn disconnect_sftp(connection_id: String) -> Result<String, String> {
    let mut connections = CONNECTIONS.lock().unwrap();
    connections.remove(&connection_id);
    Ok("连接已断开".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            test_sftp_connection,
            connect_sftp,
            list_directory,
            download_file,
            upload_file,
            create_directory,
            delete_file,
            disconnect_sftp
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
