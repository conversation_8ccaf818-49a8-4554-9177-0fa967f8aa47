use std::collections::HashMap;
use std::path::Path;
use std::sync::Mutex;
use serde::{Deserialize, Serialize};
use ssh2::Session;
use std::net::TcpStream;
use std::io::Write;
use base64::{Engine as _, engine::general_purpose};

// SFTP 连接信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SftpConnectionInfo {
    pub id: String,
    pub name: String,
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub connected: bool,
}

// 文件信息
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub is_dir: bool,
    pub modified: Option<String>,
    pub permissions: String,
}

// 传输进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransferProgress {
    pub id: String,
    pub filename: String,
    pub total_size: u64,
    pub transferred: u64,
    pub speed: f64,
    pub status: String,
}

// 全局连接管理器
type ConnectionManager = Mutex<HashMap<String, Session>>;
static CONNECTIONS: std::sync::LazyLock<ConnectionManager> =
    std::sync::LazyLock::new(|| Mutex::new(HashMap::new()));

// 测试连接命令
#[tauri::command]
async fn test_sftp_connection(
    host: String,
    port: u16,
    username: String,
    password: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let tcp = TcpStream::connect(format!("{}:{}", host, port))
            .map_err(|e| format!("连接失败: {}", e))?;

        let mut session = Session::new()
            .map_err(|e| format!("创建会话失败: {}", e))?;

        session.set_tcp_stream(tcp);
        session.handshake()
            .map_err(|e| format!("握手失败: {}", e))?;

        session.userauth_password(&username, &password)
            .map_err(|e| format!("认证失败: {}", e))?;

        if !session.authenticated() {
            return Err("认证失败".to_string());
        }

        Ok("连接成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 建立 SFTP 连接
#[tauri::command]
async fn connect_sftp(
    connection_info: SftpConnectionInfo,
) -> Result<String, String> {
    let connection_id = connection_info.id.clone();

    tokio::task::spawn_blocking(move || {
        let tcp = TcpStream::connect(format!("{}:{}", connection_info.host, connection_info.port))
            .map_err(|e| format!("连接失败: {}", e))?;

        let mut session = Session::new()
            .map_err(|e| format!("创建会话失败: {}", e))?;

        session.set_tcp_stream(tcp);
        session.handshake()
            .map_err(|e| format!("握手失败: {}", e))?;

        session.userauth_password(&connection_info.username, &connection_info.password)
            .map_err(|e| format!("认证失败: {}", e))?;

        if !session.authenticated() {
            return Err("认证失败".to_string());
        }

        // 存储连接
        let mut connections = CONNECTIONS.lock().unwrap();
        connections.insert(connection_id.clone(), session);

        Ok(connection_id)
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 列出目录内容
#[tauri::command]
async fn list_directory(
    connection_id: String,
    path: String,
) -> Result<Vec<FileInfo>, String> {
    println!("=== 开始列出目录 ===");
    println!("请求路径: '{}' (连接ID: {})", path, connection_id);

    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        println!("当前活跃连接数: {}", connections.len());

        let session = connections.get(&connection_id)
            .ok_or_else(|| {
                println!("错误: 连接不存在，连接ID: {}", connection_id);
                println!("可用连接ID: {:?}", connections.keys().collect::<Vec<_>>());
                format!("连接不存在: {}", connection_id)
            })?;

        println!("会话已找到，创建 SFTP 连接...");
        let sftp = session.sftp()
            .map_err(|e| {
                println!("创建 SFTP 会话失败: {}", e);
                format!("创建 SFTP 会话失败: {}", e)
            })?;

        // 验证路径格式
        let normalized_path = if path.is_empty() || path == "/" {
            "/"
        } else {
            &path
        };

        println!("标准化路径: '{}' -> '{}'", path, normalized_path);

        // 尝试获取当前工作目录
        match sftp.realpath(Path::new(".")) {
            Ok(real_path) => println!("当前工作目录: {}", real_path.display()),
            Err(e) => println!("无法获取当前工作目录: {}", e),
        }

        println!("尝试读取目录: '{}'", normalized_path);
        let entries = sftp.readdir(Path::new(normalized_path))
            .map_err(|e| {
                let error_msg = format!("读取目录失败 [{}]: {}", normalized_path, e);
                println!("{}", error_msg);

                // 尝试列出根目录作为备选
                if normalized_path != "/" {
                    println!("尝试读取根目录作为备选...");
                    match sftp.readdir(Path::new("/")) {
                        Ok(root_entries) => {
                            println!("根目录包含 {} 个条目", root_entries.len());
                        }
                        Err(root_err) => {
                            println!("根目录也无法读取: {}", root_err);
                        }
                    }
                }

                error_msg
            })?;

        println!("原始条目数量: {}", entries.len());

        let mut files = Vec::new();
        for (index, (entry_path, stat)) in entries.iter().enumerate() {
            println!("处理条目 {}: {:?}", index + 1, entry_path);

            // 获取文件名，如果失败则跳过这个文件
            let name = match entry_path.file_name().and_then(|n| n.to_str()) {
                Some(n) => {
                    println!("  文件名: '{}'", n);
                    n.to_string()
                },
                None => {
                    println!("  警告: 无法获取文件名，跳过: {:?}", entry_path);
                    continue;
                }
            };

            let file_info = FileInfo {
                name: name.clone(),
                path: entry_path.to_string_lossy().to_string(),
                size: stat.size.unwrap_or(0),
                is_dir: stat.is_dir(),
                modified: None, // 可以后续添加时间解析
                permissions: format!("{:o}", stat.perm.unwrap_or(0)),
            };

            println!("  -> 添加文件: {} (路径: {}, 目录: {}, 大小: {})",
                file_info.name, file_info.path, file_info.is_dir, file_info.size);
            files.push(file_info);
        }

        println!("=== 目录读取完成 ===");
        println!("最终文件数量: {} (原始: {})", files.len(), entries.len());

        if files.is_empty() {
            println!("警告: 目录为空！可能的原因:");
            println!("1. 目录确实为空");
            println!("2. 权限不足");
            println!("3. 路径不正确");
            println!("4. 文件名编码问题");
        }

        Ok(files)
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 下载文件
#[tauri::command]
async fn download_file(
    connection_id: String,
    remote_path: String,
    local_path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        let mut remote_file = sftp.open(Path::new(&remote_path))
            .map_err(|e| format!("打开远程文件失败: {}", e))?;

        let mut local_file = std::fs::File::create(&local_path)
            .map_err(|e| format!("创建本地文件失败: {}", e))?;

        std::io::copy(&mut remote_file, &mut local_file)
            .map_err(|e| format!("文件传输失败: {}", e))?;

        Ok("下载完成".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 上传文件
#[tauri::command]
async fn upload_file(
    connection_id: String,
    local_path: String,
    remote_path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        let mut local_file = std::fs::File::open(&local_path)
            .map_err(|e| format!("打开本地文件失败: {}", e))?;

        let mut remote_file = sftp.create(Path::new(&remote_path))
            .map_err(|e| format!("创建远程文件失败: {}", e))?;

        std::io::copy(&mut local_file, &mut remote_file)
            .map_err(|e| format!("文件传输失败: {}", e))?;

        Ok("上传完成".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 上传文件数据（从前端传来的 base64 数据）
#[tauri::command]
async fn upload_file_data(
    connection_id: String,
    remote_path: String,
    file_data: String,
    file_name: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        // 解码 base64 数据
        let decoded_data = general_purpose::STANDARD.decode(&file_data)
            .map_err(|e| format!("解码文件数据失败: {}", e))?;

        let mut remote_file = sftp.create(Path::new(&remote_path))
            .map_err(|e| format!("创建远程文件失败: {}", e))?;

        remote_file.write_all(&decoded_data)
            .map_err(|e| format!("写入文件数据失败: {}", e))?;

        Ok(format!("文件 {} 上传完成", file_name))
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 创建目录
#[tauri::command]
async fn create_directory(
    connection_id: String,
    path: String,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        sftp.mkdir(Path::new(&path), 0o755)
            .map_err(|e| format!("创建目录失败: {}", e))?;

        Ok("目录创建成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 删除文件或目录
#[tauri::command]
async fn delete_file(
    connection_id: String,
    path: String,
    is_dir: bool,
) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();
        let session = connections.get(&connection_id)
            .ok_or("连接不存在")?;

        let sftp = session.sftp()
            .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

        if is_dir {
            sftp.rmdir(Path::new(&path))
                .map_err(|e| format!("删除目录失败: {}", e))?;
        } else {
            sftp.unlink(Path::new(&path))
                .map_err(|e| format!("删除文件失败: {}", e))?;
        }

        Ok("删除成功".to_string())
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 获取连接状态和信息
#[tauri::command]
async fn get_connection_info(connection_id: String) -> Result<String, String> {
    tokio::task::spawn_blocking(move || {
        let connections = CONNECTIONS.lock().unwrap();

        if let Some(session) = connections.get(&connection_id) {
            let sftp = session.sftp()
                .map_err(|e| format!("创建 SFTP 会话失败: {}", e))?;

            // 尝试获取当前工作目录
            match sftp.realpath(Path::new(".")) {
                Ok(cwd) => Ok(format!("连接活跃，当前目录: {}", cwd.display())),
                Err(e) => Ok(format!("连接活跃，但无法获取当前目录: {}", e)),
            }
        } else {
            Err(format!("连接不存在: {}", connection_id))
        }
    }).await.map_err(|e| format!("任务执行失败: {}", e))?
}

// 断开连接
#[tauri::command]
async fn disconnect_sftp(connection_id: String) -> Result<String, String> {
    let mut connections = CONNECTIONS.lock().unwrap();
    connections.remove(&connection_id);
    Ok("连接已断开".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            test_sftp_connection,
            connect_sftp,
            list_directory,
            download_file,
            upload_file,
            upload_file_data,
            create_directory,
            delete_file,
            get_connection_info,
            disconnect_sftp
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
