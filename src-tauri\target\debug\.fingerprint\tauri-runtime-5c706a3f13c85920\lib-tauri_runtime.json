{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 17993515530118038896, "deps": [[442785307232013896, "build_script_build", false, 10049441755064117178], [3150220818285335163, "url", false, 5603857225676842057], [4143744114649553716, "raw_window_handle", false, 7517658832036738009], [7606335748176206944, "dpi", false, 5302088904103583172], [9010263965687315507, "http", false, 13014744829488832296], [9689903380558560274, "serde", false, 6121258471593067390], [10806645703491011684, "thiserror", false, 17452964001142711969], [11050281405049894993, "tauri_utils", false, 2183773089796371511], [14585479307175734061, "windows", false, 13425054998928322741], [15367738274754116744, "serde_json", false, 9722110262108598114], [16727543399706004146, "cookie", false, 9959257689103225494]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-5c706a3f13c85920\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}