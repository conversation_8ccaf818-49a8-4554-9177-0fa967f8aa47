{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 9063727828685540298, "deps": [[3060637413840920116, "proc_macro2", false, 9898791222778704133], [7341521034400937459, "tauri_codegen", false, 1843627920130116007], [10640660562325816595, "syn", false, 5048777514585564967], [11050281405049894993, "tauri_utils", false, 1252227277618345100], [13077543566650298139, "heck", false, 16773306763149267354], [17990358020177143287, "quote", false, 13527854320717894611]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-2d47cf12c424aa2a\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}