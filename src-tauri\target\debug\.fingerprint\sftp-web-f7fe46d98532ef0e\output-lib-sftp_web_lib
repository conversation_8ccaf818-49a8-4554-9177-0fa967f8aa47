{"$message_type":"diagnostic","message":"hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\incremental\\sftp_web_lib-21cu1231s4pf2\\s-h8j231sv8b-0gb5rsd-working`","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\incremental\\sftp_web_lib-21cu1231s4pf2\\s-h8j231sv8b-0gb5rsd-working`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 1 warning emitted\u001b[0m\n\n"}
