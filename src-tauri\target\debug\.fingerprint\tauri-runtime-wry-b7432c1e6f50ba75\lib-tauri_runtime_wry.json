{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 10451378034595227560, "deps": [[376837177317575824, "softbuffer", false, 1257530495297040127], [442785307232013896, "tauri_runtime", false, 7367355263642602708], [3150220818285335163, "url", false, 5603857225676842057], [3722963349756955755, "once_cell", false, 2610665209663240370], [4143744114649553716, "raw_window_handle", false, 7517658832036738009], [5986029879202738730, "log", false, 7236839104650837312], [7752760652095876438, "build_script_build", false, 7605890276426213681], [8539587424388551196, "webview2_com", false, 13234043157692173674], [9010263965687315507, "http", false, 13014744829488832296], [11050281405049894993, "tauri_utils", false, 2183773089796371511], [13223659721939363523, "tao", false, 8619538408656081527], [14585479307175734061, "windows", false, 13425054998928322741], [14794439852947137341, "wry", false, 3430714729774247469]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-b7432c1e6f50ba75\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}