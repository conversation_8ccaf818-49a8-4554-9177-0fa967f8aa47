<template>
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- 路径导航 -->
    <div class="card-elegant p-4 animate-fade-in">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <button 
            @click="navigateUp"
            :disabled="currentPath === '/'"
            class="btn btn-ghost btn-sm"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            返回上级
          </button>
          
          <div class="breadcrumbs text-sm">
            <ul>
              <li v-for="(segment, index) in pathSegments" :key="index">
                <button 
                  @click="navigateToPath(getPathUpTo(index))"
                  class="hover:text-blue-600 transition-colors"
                >
                  {{ segment || '根目录' }}
                </button>
              </li>
            </ul>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <button @click="refreshDirectory" class="btn btn-ghost btn-sm">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            刷新
          </button>
          
          <button @click="showCreateDialog = true" class="btn btn-primary btn-sm">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            新建文件夹
          </button>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="card-elegant animate-scale-in">
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr class="bg-gradient-to-r from-blue-50 to-purple-50">
              <th class="font-semibold text-gray-700">名称</th>
              <th class="font-semibold text-gray-700">大小</th>
              <th class="font-semibold text-gray-700">权限</th>
              <th class="font-semibold text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="isLoading">
              <td colspan="4" class="text-center py-8">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="animate-spin w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-gray-600">加载中...</span>
                </div>
              </td>
            </tr>
            
            <tr v-else-if="files.length === 0">
              <td colspan="4" class="text-center py-8 text-gray-500">
                此目录为空
              </td>
            </tr>
            
            <tr 
              v-else
              v-for="file in files" 
              :key="file.path"
              class="hover:bg-blue-50 transition-colors cursor-pointer"
              @dblclick="handleFileDoubleClick(file)"
            >
              <td class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg v-if="file.is_dir" class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                  </svg>
                  <svg v-else class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">{{ file.name }}</div>
                  <div class="text-sm text-gray-500">{{ file.path }}</div>
                </div>
              </td>
              
              <td class="text-gray-600">
                {{ file.is_dir ? '-' : formatFileSize(file.size) }}
              </td>
              
              <td class="text-gray-600 font-mono text-sm">
                {{ file.permissions }}
              </td>
              
              <td>
                <div class="flex items-center space-x-2">
                  <button 
                    v-if="!file.is_dir"
                    @click="downloadFile(file)"
                    class="btn btn-ghost btn-xs text-green-600 hover:text-green-700"
                    title="下载"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  </button>
                  
                  <button 
                    @click="deleteFile(file)"
                    class="btn btn-ghost btn-xs text-red-600 hover:text-red-700"
                    title="删除"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 拖拽上传区域 -->
    <div
      class="card-elegant p-8 border-2 border-dashed border-blue-300 hover:border-blue-500 transition-colors animate-slide-up"
      @dragover.prevent
      @drop.prevent="handleFileDrop"
      @click="triggerFileSelect"
    >
      <div class="text-center cursor-pointer">
        <svg class="w-12 h-12 mx-auto text-blue-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
        </svg>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">拖拽文件到此处上传</h3>
        <p class="text-gray-600">或者
          <span class="text-blue-600 hover:text-blue-700 font-medium">点击选择文件</span>
        </p>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInput"
        type="file"
        multiple
        class="hidden"
        @change="handleFileSelect"
      />
    </div>

    <!-- 新建文件夹对话框 -->
    <div v-if="showCreateDialog" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">新建文件夹</h3>
        <div class="form-control">
          <label class="label">
            <span class="label-text">文件夹名称</span>
          </label>
          <input 
            v-model="newFolderName"
            type="text" 
            placeholder="输入文件夹名称"
            class="input input-bordered w-full"
            @keyup.enter="createFolder"
          />
        </div>
        <div class="modal-action">
          <button @click="showCreateDialog = false" class="btn btn-ghost">取消</button>
          <button @click="createFolder" class="btn btn-primary">创建</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { invoke } from '@tauri-apps/api/core';
import { useNotification } from '../composables/useNotification';

// Props
const props = defineProps<{
  connection: any;
}>();

// Emits
const emit = defineEmits<{
  transferStart: [transfer: any];
}>();

// 通知系统
const { success, error, warning, info } = useNotification();

// 响应式数据
const currentPath = ref('/');
const files = ref<any[]>([]);
const isLoading = ref(false);
const showCreateDialog = ref(false);
const newFolderName = ref('');
const fileInput = ref<HTMLInputElement>();

// 计算属性
const pathSegments = computed(() => {
  if (currentPath.value === '/') {
    return [''];
  }
  return currentPath.value.split('/').filter(Boolean);
});

// 方法
const getPathUpTo = (index: number) => {
  if (index === 0 && pathSegments.value[0] === '') {
    return '/';
  }
  const segments = pathSegments.value.slice(0, index + 1);
  return '/' + segments.join('/');
};

const navigateUp = () => {
  if (currentPath.value === '/') return;

  // 移除末尾的斜杠（如果有的话）
  let path = currentPath.value.endsWith('/') && currentPath.value !== '/'
    ? currentPath.value.slice(0, -1)
    : currentPath.value;

  // 找到最后一个斜杠的位置
  const lastSlashIndex = path.lastIndexOf('/');

  // 如果是根目录下的文件夹，返回根目录
  if (lastSlashIndex === 0) {
    navigateToPath('/');
  } else if (lastSlashIndex > 0) {
    // 否则返回上一级目录
    const parentPath = path.substring(0, lastSlashIndex);
    navigateToPath(parentPath);
  }
};

const navigateToPath = (path: string) => {
  // 确保路径格式正确
  let normalizedPath = path;
  if (!normalizedPath.startsWith('/')) {
    normalizedPath = '/' + normalizedPath;
  }
  // 移除末尾的斜杠（除非是根目录）
  if (normalizedPath !== '/' && normalizedPath.endsWith('/')) {
    normalizedPath = normalizedPath.slice(0, -1);
  }

  currentPath.value = normalizedPath;
  loadDirectory();
};

const loadDirectory = async () => {
  isLoading.value = true;
  try {
    const result = await invoke('list_directory', {
      connectionId: props.connection.id,
      path: currentPath.value
    });
    files.value = result as any[];
  } catch (err) {
    error('加载目录失败', err as string);
    console.error('加载目录失败:', err);
  } finally {
    isLoading.value = false;
  }
};

const refreshDirectory = () => {
  loadDirectory();
};

const handleFileDoubleClick = (file: any) => {
  if (file.is_dir) {
    // 确保路径正确拼接
    let newPath = file.path;
    if (!newPath.startsWith('/')) {
      newPath = currentPath.value === '/'
        ? '/' + file.name
        : currentPath.value + '/' + file.name;
    }
    navigateToPath(newPath);
  }
};

const downloadFile = async (file: any) => {
  try {
    info('开始下载', `正在下载文件: ${file.name}`);

    // 创建一个隐藏的文件输入元素来选择保存位置
    // 由于浏览器安全限制，我们先下载到默认位置
    const downloadsPath = await getDownloadsPath();
    const localPath = `${downloadsPath}/${file.name}`;

    // 发送传输开始事件
    const transferId = `download_${Date.now()}`;
    emit('transferStart', {
      id: transferId,
      filename: file.name,
      type: 'download',
      status: 'transferring',
      total_size: file.size,
      transferred: 0
    });

    await invoke('download_file', {
      connectionId: props.connection.id,
      remotePath: file.path,
      localPath
    });

    success('下载完成', `文件 ${file.name} 已下载到 Downloads 文件夹`);

    // 更新传输状态为完成
    emit('transferStart', {
      id: transferId,
      filename: file.name,
      type: 'download',
      status: 'completed',
      total_size: file.size,
      transferred: file.size
    });

  } catch (err) {
    error('下载失败', `文件 ${file.name} 下载失败: ${err}`);
    console.error('下载失败:', err);
  }
};

// 获取下载文件夹路径的辅助函数
const getDownloadsPath = async () => {
  try {
    // 在实际应用中，这里应该使用 Tauri 的路径 API
    // 暂时使用相对路径
    return './downloads';
  } catch {
    return './downloads';
  }
};

const deleteFile = async (file: any) => {
  if (!confirm(`确定要删除 ${file.name} 吗？`)) return;

  try {
    info('正在删除', `正在删除 ${file.is_dir ? '文件夹' : '文件'}: ${file.name}`);
    await invoke('delete_file', {
      connectionId: props.connection.id,
      path: file.path,
      isDir: file.is_dir
    });

    success('删除成功', `${file.is_dir ? '文件夹' : '文件'} ${file.name} 已删除`);
    await loadDirectory();
  } catch (err) {
    error('删除失败', `删除 ${file.name} 失败: ${err}`);
    console.error('删除失败:', err);
  }
};

const createFolder = async () => {
  if (!newFolderName.value.trim()) {
    warning('名称不能为空', '请输入文件夹名称');
    return;
  }

  try {
    const folderPath = `${currentPath.value}/${newFolderName.value}`.replace('//', '/');
    info('正在创建', `正在创建文件夹: ${newFolderName.value}`);
    await invoke('create_directory', {
      connectionId: props.connection.id,
      path: folderPath
    });

    success('创建成功', `文件夹 ${newFolderName.value} 创建成功`);
    showCreateDialog.value = false;
    newFolderName.value = '';
    await loadDirectory();
  } catch (err) {
    error('创建失败', `创建文件夹失败: ${err}`);
    console.error('创建文件夹失败:', err);
  }
};

// 触发文件选择
const triggerFileSelect = () => {
  fileInput.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const selectedFiles = target.files;
  if (selectedFiles && selectedFiles.length > 0) {
    handleFileUpload(Array.from(selectedFiles));
  }
  // 清空输入，允许重复选择同一文件
  target.value = '';
};

// 处理拖拽文件
const handleFileDrop = async (event: DragEvent) => {
  const droppedFiles = event.dataTransfer?.files;
  if (droppedFiles && droppedFiles.length > 0) {
    handleFileUpload(Array.from(droppedFiles));
  }
};

// 处理文件上传
const handleFileUpload = async (fileList: File[]) => {
  info('开始上传', `准备上传 ${fileList.length} 个文件`);

  for (const file of fileList) {
    await uploadFile(file);
  }
};

const uploadFile = async (file: File) => {
  try {
    const remotePath = `${currentPath.value}/${file.name}`.replace('//', '/');

    info('正在上传', `正在上传文件: ${file.name}`);

    // 发送传输开始事件
    const transferId = `upload_${Date.now()}`;
    emit('transferStart', {
      id: transferId,
      filename: file.name,
      type: 'upload',
      status: 'transferring',
      total_size: file.size,
      transferred: 0
    });

    // 将文件转换为 ArrayBuffer 然后转为 base64
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    const base64Data = btoa(String.fromCharCode(...uint8Array));

    // 调用后端上传函数
    await invoke('upload_file_data', {
      connectionId: props.connection.id,
      remotePath,
      fileData: base64Data,
      fileName: file.name
    });

    success('上传完成', `文件 ${file.name} 上传完成`);

    // 更新传输状态为完成
    emit('transferStart', {
      id: transferId,
      filename: file.name,
      type: 'upload',
      status: 'completed',
      total_size: file.size,
      transferred: file.size
    });

    // 刷新目录
    await loadDirectory();

  } catch (err) {
    error('上传失败', `文件 ${file.name} 上传失败: ${err}`);
    console.error('上传失败:', err);
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 生命周期
onMounted(() => {
  loadDirectory();
});

// 监听连接变化
watch(() => props.connection, () => {
  currentPath.value = '/';
  loadDirectory();
});
</script>
