{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 13667997004821700218, "deps": [[561782849581144631, "html5ever", false, 3033159854839082278], [1200537532907108615, "url<PERSON><PERSON>n", false, 6512860506318385237], [3150220818285335163, "url", false, 5603857225676842057], [3191507132440681679, "serde_untagged", false, 8099541304412984265], [4899080583175475170, "semver", false, 8826166672336857875], [5578504951057029730, "serde_with", false, 11219253636574709042], [5986029879202738730, "log", false, 7236839104650837312], [6262254372177975231, "kuchiki", false, 13661380985319407654], [6606131838865521726, "ctor", false, 14182983749263760031], [7170110829644101142, "json_patch", false, 17566065551534202534], [8319709847752024821, "uuid", false, 13406518202908906290], [9010263965687315507, "http", false, 13014744829488832296], [9451456094439810778, "regex", false, 10426007333226538973], [9689903380558560274, "serde", false, 6121258471593067390], [10806645703491011684, "thiserror", false, 17452964001142711969], [11989259058781683633, "dunce", false, 15514511650017520897], [13625485746686963219, "anyhow", false, 3520052488611987028], [14132538657330703225, "brotli", false, 4352199974123728902], [15367738274754116744, "serde_json", false, 9722110262108598114], [15609422047640926750, "toml", false, 14925062934262769137], [15622660310229662834, "walkdir", false, 2105496246354289571], [15932120279885307830, "memchr", false, 16952204854623452993], [17146114186171651583, "infer", false, 17232285599674687432], [17155886227862585100, "glob", false, 271191148063710908], [17186037756130803222, "phf", false, 1601336731437146261]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-c742b263e29f12d0\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}