["\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\tray\\autogenerated\\default.toml"]