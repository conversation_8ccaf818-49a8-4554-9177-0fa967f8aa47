{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 11523288545049160926, "deps": [[9689903380558560274, "serde", false, 99868426957841695], [11690957875220028834, "serde_with_macros", false, 17616310792233290530], [16257276029081467297, "serde_derive", false, 16553805606954248794]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-9e3b9e1ae726d69b\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}