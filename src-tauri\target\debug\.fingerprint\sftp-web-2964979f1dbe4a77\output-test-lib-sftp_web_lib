{"$message_type":"diagnostic","message":"expected `;`, found keyword `if`","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":5343,"byte_end":5345,"line_start":167,"line_end":167,"column_start":9,"column_end":11,"is_primary":false,"text":[{"text":"        if bytes_read == 0 {","highlight_start":9,"highlight_end":11}],"label":"unexpected token","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5333,"byte_end":5333,"line_start":166,"line_end":166,"column_start":68,"column_end":68,"is_primary":true,"text":[{"text":"    .map_err(|e| format!(\"Failed to read from local file: {}\", e))?","highlight_start":68,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"add `;` here","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5333,"byte_end":5333,"line_start":166,"line_end":166,"column_start":68,"column_end":68,"is_primary":true,"text":[{"text":"    .map_err(|e| format!(\"Failed to read from local file: {}\", e))?","highlight_start":68,"highlight_end":68}],"label":null,"suggested_replacement":";","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: expected `;`, found keyword `if`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:166:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    .map_err(|e| format!(\"Failed to read from local file: {}\", e))?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: add `;` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if bytes_read == 0 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected token\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the name `Duration` is defined multiple times","code":{"code":"E0252","explanation":"Two items of the same name cannot be imported without rebinding one of the\nitems under a new local name.\n\nErroneous code example:\n\n```compile_fail,E0252\nuse foo::baz;\nuse bar::baz; // error, do `use bar::baz as quux` instead\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse foo::baz as foo_baz;\nuse bar::baz; // ok!\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nOr you can reference the item with its parent:\n\n```\nuse bar::baz;\n\nfn main() {\n    let x = foo::baz; // ok!\n}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":580,"byte_end":599,"line_start":21,"line_end":21,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":"`Duration` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":356,"byte_end":375,"line_start":12,"line_end":12,"column_start":5,"column_end":24,"is_primary":false,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":"previous import of the type `Duration` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Duration` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":576,"byte_end":602,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0252]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `Duration` is defined multiple times\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the type `Duration` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`Duration` reimported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Duration` must be defined only once in the type namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected `;`","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":5343,"byte_end":5345,"line_start":167,"line_end":167,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        if bytes_read == 0 {","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: expected `;`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:167:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if bytes_read == 0 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `async_std::io::AsyncWriteExt`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":140,"byte_end":153,"line_start":5,"line_end":5,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use async_std::io::{ReadExt, Write, AsyncWriteExt};","highlight_start":37,"highlight_end":50}],"label":"no `AsyncWriteExt` in `io`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these traits instead:\nfutures::AsyncWriteExt\ntokio::io::AsyncWriteExt","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `async_std::io::AsyncWriteExt`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:5:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_std::io::{ReadExt, Write, AsyncWriteExt};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AsyncWriteExt` in `io`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing one of these traits instead:\u001b[0m\n\u001b[0m          futures::AsyncWriteExt\u001b[0m\n\u001b[0m          tokio::io::AsyncWriteExt\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `russh::auth::AuthMethod`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":161,"byte_end":184,"line_start":6,"line_end":6,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use russh::auth::AuthMethod;","highlight_start":5,"highlight_end":28}],"label":"no `AuthMethod` in `auth`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `russh::auth::AuthMethod`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh::auth::AuthMethod;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AuthMethod` in `auth`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `russh::client::Userauth`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":191,"byte_end":219,"line_start":7,"line_end":7,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"use russh::client::Userauth as _;","highlight_start":5,"highlight_end":33}],"label":"no `Userauth` in `client`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `russh::client::Userauth`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh::client::Userauth as _;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `Userauth` in `client`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `russh_sftp::SftpSession`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":326,"byte_end":349,"line_start":11,"line_end":11,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use russh_sftp::SftpSession;","highlight_start":5,"highlight_end":28}],"label":"no `SftpSession` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct instead","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":326,"byte_end":349,"line_start":11,"line_end":11,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use russh_sftp::SftpSession;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":"russh_sftp::client::SftpSession","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `russh_sftp::SftpSession`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh_sftp::SftpSession;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `SftpSession` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;9mrussh_sftp::SftpSession\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;10mrussh_sftp::client::SftpSession\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `russh::Session`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":448,"byte_end":462,"line_start":16,"line_end":16,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use russh::Session;","highlight_start":5,"highlight_end":19}],"label":"no `Session` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":455,"byte_end":462,"line_start":16,"line_end":16,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"use russh::Session;","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":"session","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"consider importing one of these structs instead","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":448,"byte_end":462,"line_start":16,"line_end":16,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use russh::Session;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"russh::client::Session","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\lib.rs","byte_start":448,"byte_end":462,"line_start":16,"line_end":16,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use russh::Session;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"russh::server::Session","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `russh::Session`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh::Session;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `Session` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module (notice the capitalization difference)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0muse russh::\u001b[0m\u001b[0m\u001b[38;5;9mSession\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0muse russh::\u001b[0m\u001b[0m\u001b[38;5;10msession\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these structs instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;9mrussh::Session\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;10mrussh::client::Session\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;9mrussh::Session\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0muse \u001b[0m\u001b[0m\u001b[38;5;10mrussh::server::Session\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `russh::message`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":505,"byte_end":512,"line_start":18,"line_end":18,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"use russh::message::Message;","highlight_start":12,"highlight_end":19}],"label":"could not find `message` in `russh`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `russh::message`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:18:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh::message::Message;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `message` in `russh`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find macro `__cmd__sftp_upload` in this scope","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":5713,"byte_end":5737,"line_start":181,"line_end":181,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"#[tauri::command(async)]","highlight_start":1,"highlight_end":25}],"label":"similarly named macro `__cmd__sftp_download` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5713,"byte_end":5737,"line_start":181,"line_end":181,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"#[tauri::command(async)]","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[tauri::command]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\tauri-macros-2.2.0\\src\\lib.rs","byte_start":1025,"byte_end":1098,"line_start":35,"line_end":35,"column_start":1,"column_end":74,"is_primary":false,"text":[{"text":"pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":7173,"byte_end":7184,"line_start":218,"line_end":218,"column_start":98,"column_end":109,"is_primary":true,"text":[{"text":"        .invoke_handler(tauri::generate_handler![sftp_connect, sftp_list_files, sftp_disconnect, sftp_upload, sftp_download])","highlight_start":98,"highlight_end":109}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a macro with a similar name exists","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":7173,"byte_end":7184,"line_start":218,"line_end":218,"column_start":98,"column_end":109,"is_primary":true,"text":[{"text":"        .invoke_handler(tauri::generate_handler![sftp_connect, sftp_list_files, sftp_disconnect, sftp_upload, sftp_download])","highlight_start":98,"highlight_end":109}],"label":null,"suggested_replacement":"__cmd__sftp_download","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find macro `__cmd__sftp_upload` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:218:98\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[tauri::command(async)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14msimilarly named macro `__cmd__sftp_download` defined here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .invoke_handler(tauri::generate_handler![sftp_connect, sftp_list_files, sftp_disconnect, sftp_upload, sftp_download])\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a macro with a similar name exists: `__cmd__sftp_download`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type `FutureUnit` is not a member of trait `Handler`","code":{"code":"E0437","explanation":"An associated type whose name does not match any of the associated types\nin the trait was used when implementing the trait.\n\nErroneous code example:\n\n```compile_fail,E0437\ntrait Foo {}\n\nimpl Foo for i32 {\n    type Bar = bool;\n}\n```\n\nTrait implementations can only implement associated types that are members of\nthe trait in question.\n\nThe solution to this problem is to remove the extraneous associated type:\n\n```\ntrait Foo {}\n\nimpl Foo for i32 {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":818,"byte_end":880,"line_start":35,"line_end":35,"column_start":5,"column_end":67,"is_primary":true,"text":[{"text":"    type FutureUnit = BoxFuture<'static, Result<(), Self::Error>>;","highlight_start":5,"highlight_end":67}],"label":"not a member of trait `Handler`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0437]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type `FutureUnit` is not a member of trait `Handler`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:35:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    type FutureUnit = BoxFuture<'static, Result<(), Self::Error>>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `Handler`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type `FutureBool` is not a member of trait `Handler`","code":{"code":"E0437","explanation":"An associated type whose name does not match any of the associated types\nin the trait was used when implementing the trait.\n\nErroneous code example:\n\n```compile_fail,E0437\ntrait Foo {}\n\nimpl Foo for i32 {\n    type Bar = bool;\n}\n```\n\nTrait implementations can only implement associated types that are members of\nthe trait in question.\n\nThe solution to this problem is to remove the extraneous associated type:\n\n```\ntrait Foo {}\n\nimpl Foo for i32 {}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":886,"byte_end":950,"line_start":36,"line_end":36,"column_start":5,"column_end":69,"is_primary":true,"text":[{"text":"    type FutureBool = BoxFuture<'static, Result<bool, Self::Error>>;","highlight_start":5,"highlight_end":69}],"label":"not a member of trait `Handler`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0437]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type `FutureBool` is not a member of trait `Handler`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:36:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    type FutureBool = BoxFuture<'static, Result<bool, Self::Error>>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `Handler`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `channel_open_session` is not a member of trait `Handler`","code":{"code":"E0407","explanation":"A definition of a method not in the implemented trait was given in a trait\nimplementation.\n\nErroneous code example:\n\n```compile_fail,E0407\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // error: method `b` is not a member of trait `Foo`\n}\n```\n\nPlease verify you didn't misspell the method name and you used the correct\ntrait. First example:\n\n```\ntrait Foo {\n    fn a();\n    fn b();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // ok!\n}\n```\n\nSecond example:\n\n```\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n}\n\nimpl Bar {\n    fn b() {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1121,"byte_end":1234,"line_start":42,"line_end":44,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    fn channel_open_session(&mut self) -> Self::FutureUnit {","highlight_start":5,"highlight_end":61},{"text":"        Box::pin(futures::future::ready(Ok(())))","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"not a member of trait `Handler`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0407]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `channel_open_session` is not a member of trait `Handler`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:42:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn channel_open_session(&mut self) -> Self::FutureUnit {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Box::pin(futures::future::ready(Ok(())))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `Handler`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"implicit elided lifetime not allowed here","code":{"code":"E0726","explanation":"An argument lifetime was elided in an async function.\n\nErroneous code example:\n\nWhen a struct or a type is bound/declared with a lifetime it is important for\nthe Rust compiler to know, on usage, the lifespan of the type. When the\nlifetime is not explicitly mentioned and the Rust Compiler cannot determine\nthe lifetime of your type, the following error occurs.\n\n```compile_fail,E0726\nuse futures::executor::block_on;\nstruct Content<'a> {\n    title: &'a str,\n    body: &'a str,\n}\nasync fn create(content: Content) { // error: implicit elided\n                                    // lifetime not allowed here\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\nlet content = Content { title: \"Rust\", body: \"is great!\" };\nlet future = create(content);\nblock_on(future);\n```\n\nSpecify desired lifetime of parameter `content` or indicate the anonymous\nlifetime like `content: Content<'_>`. The anonymous lifetime tells the Rust\ncompiler that `content` is only needed until the `create` function is done with\nits execution.\n\nThe `implicit elision` meaning the omission of suggested lifetime that is\n`pub async fn create<'a>(content: Content<'a>) {}` is not allowed here as\nlifetime of the `content` can differ from current context:\n\n```ignore (needs futures dependency)\nasync fn create(content: Content<'_>) { // ok!\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\n```\n\nKnow more about lifetime elision in this [chapter][lifetime-elision] and a\nchapter on lifetimes can be found [here][lifetimes].\n\n[lifetime-elision]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n[lifetimes]: https://doc.rust-lang.org/rust-by-example/scope/lifetime.html\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1574,"byte_end":1602,"line_start":63,"line_end":63,"column_start":12,"column_end":40,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":12,"highlight_end":40}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1580,"byte_end":1580,"line_start":63,"line_end":63,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'_, ","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0726]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: implicit elided lifetime not allowed here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:63:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<Arc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected lifetime parameter\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    state: State<\u001b[0m\u001b[0m\u001b[38;5;10m'_, \u001b[0m\u001b[0mArc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"implicit elided lifetime not allowed here","code":{"code":"E0726","explanation":"An argument lifetime was elided in an async function.\n\nErroneous code example:\n\nWhen a struct or a type is bound/declared with a lifetime it is important for\nthe Rust compiler to know, on usage, the lifespan of the type. When the\nlifetime is not explicitly mentioned and the Rust Compiler cannot determine\nthe lifetime of your type, the following error occurs.\n\n```compile_fail,E0726\nuse futures::executor::block_on;\nstruct Content<'a> {\n    title: &'a str,\n    body: &'a str,\n}\nasync fn create(content: Content) { // error: implicit elided\n                                    // lifetime not allowed here\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\nlet content = Content { title: \"Rust\", body: \"is great!\" };\nlet future = create(content);\nblock_on(future);\n```\n\nSpecify desired lifetime of parameter `content` or indicate the anonymous\nlifetime like `content: Content<'_>`. The anonymous lifetime tells the Rust\ncompiler that `content` is only needed until the `create` function is done with\nits execution.\n\nThe `implicit elision` meaning the omission of suggested lifetime that is\n`pub async fn create<'a>(content: Content<'a>) {}` is not allowed here as\nlifetime of the `content` can differ from current context:\n\n```ignore (needs futures dependency)\nasync fn create(content: Content<'_>) { // ok!\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\n```\n\nKnow more about lifetime elision in this [chapter][lifetime-elision] and a\nchapter on lifetimes can be found [here][lifetimes].\n\n[lifetime-elision]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n[lifetimes]: https://doc.rust-lang.org/rust-by-example/scope/lifetime.html\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2965,"byte_end":2993,"line_start":108,"line_end":108,"column_start":12,"column_end":40,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":12,"highlight_end":40}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2971,"byte_end":2971,"line_start":108,"line_end":108,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'_, ","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0726]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: implicit elided lifetime not allowed here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:108:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<Arc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    state: State<\u001b[0m\u001b[0m\u001b[38;5;10m'_, \u001b[0m\u001b[0mArc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"implicit elided lifetime not allowed here","code":{"code":"E0726","explanation":"An argument lifetime was elided in an async function.\n\nErroneous code example:\n\nWhen a struct or a type is bound/declared with a lifetime it is important for\nthe Rust compiler to know, on usage, the lifespan of the type. When the\nlifetime is not explicitly mentioned and the Rust Compiler cannot determine\nthe lifetime of your type, the following error occurs.\n\n```compile_fail,E0726\nuse futures::executor::block_on;\nstruct Content<'a> {\n    title: &'a str,\n    body: &'a str,\n}\nasync fn create(content: Content) { // error: implicit elided\n                                    // lifetime not allowed here\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\nlet content = Content { title: \"Rust\", body: \"is great!\" };\nlet future = create(content);\nblock_on(future);\n```\n\nSpecify desired lifetime of parameter `content` or indicate the anonymous\nlifetime like `content: Content<'_>`. The anonymous lifetime tells the Rust\ncompiler that `content` is only needed until the `create` function is done with\nits execution.\n\nThe `implicit elision` meaning the omission of suggested lifetime that is\n`pub async fn create<'a>(content: Content<'a>) {}` is not allowed here as\nlifetime of the `content` can differ from current context:\n\n```ignore (needs futures dependency)\nasync fn create(content: Content<'_>) { // ok!\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\n```\n\nKnow more about lifetime elision in this [chapter][lifetime-elision] and a\nchapter on lifetimes can be found [here][lifetimes].\n\n[lifetime-elision]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n[lifetimes]: https://doc.rust-lang.org/rust-by-example/scope/lifetime.html\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":4105,"byte_end":4133,"line_start":136,"line_end":136,"column_start":33,"column_end":61,"is_primary":true,"text":[{"text":"async fn sftp_disconnect(state: State<Arc<Mutex<SftpState>>>) -> Result<String, String> {","highlight_start":33,"highlight_end":61}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":4111,"byte_end":4111,"line_start":136,"line_end":136,"column_start":39,"column_end":39,"is_primary":true,"text":[{"text":"async fn sftp_disconnect(state: State<Arc<Mutex<SftpState>>>) -> Result<String, String> {","highlight_start":39,"highlight_end":39}],"label":null,"suggested_replacement":"'_, ","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0726]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: implicit elided lifetime not allowed here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:136:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn sftp_disconnect(state: State<Arc<Mutex<SftpState>>>) -> Result<String, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0masync fn sftp_disconnect(state: State<\u001b[0m\u001b[0m\u001b[38;5;10m'_, \u001b[0m\u001b[0mArc<Mutex<SftpState>>>) -> Result<String, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"implicit elided lifetime not allowed here","code":{"code":"E0726","explanation":"An argument lifetime was elided in an async function.\n\nErroneous code example:\n\nWhen a struct or a type is bound/declared with a lifetime it is important for\nthe Rust compiler to know, on usage, the lifespan of the type. When the\nlifetime is not explicitly mentioned and the Rust Compiler cannot determine\nthe lifetime of your type, the following error occurs.\n\n```compile_fail,E0726\nuse futures::executor::block_on;\nstruct Content<'a> {\n    title: &'a str,\n    body: &'a str,\n}\nasync fn create(content: Content) { // error: implicit elided\n                                    // lifetime not allowed here\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\nlet content = Content { title: \"Rust\", body: \"is great!\" };\nlet future = create(content);\nblock_on(future);\n```\n\nSpecify desired lifetime of parameter `content` or indicate the anonymous\nlifetime like `content: Content<'_>`. The anonymous lifetime tells the Rust\ncompiler that `content` is only needed until the `create` function is done with\nits execution.\n\nThe `implicit elision` meaning the omission of suggested lifetime that is\n`pub async fn create<'a>(content: Content<'a>) {}` is not allowed here as\nlifetime of the `content` can differ from current context:\n\n```ignore (needs futures dependency)\nasync fn create(content: Content<'_>) { // ok!\n    println!(\"title: {}\", content.title);\n    println!(\"body: {}\", content.body);\n}\n```\n\nKnow more about lifetime elision in this [chapter][lifetime-elision] and a\nchapter on lifetimes can be found [here][lifetimes].\n\n[lifetime-elision]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n[lifetimes]: https://doc.rust-lang.org/rust-by-example/scope/lifetime.html\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":5775,"byte_end":5803,"line_start":183,"line_end":183,"column_start":12,"column_end":40,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":12,"highlight_end":40}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5781,"byte_end":5781,"line_start":183,"line_end":183,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    state: State<Arc<Mutex<SftpState>>>,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'_, ","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0726]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: implicit elided lifetime not allowed here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:183:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m183\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<Arc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m183\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    state: State<\u001b[0m\u001b[0m\u001b[38;5;10m'_, \u001b[0m\u001b[0mArc<Mutex<SftpState>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module `auth` is private","code":{"code":"E0603","explanation":"A private item was used outside its scope.\n\nErroneous code example:\n\n```compile_fail,E0603\nmod foo {\n    const PRIVATE: u32 = 0x_a_bad_1dea_u32; // This const is private, so we\n                                            // can't use it outside of the\n                                            // `foo` module.\n}\n\nprintln!(\"const value: {}\", foo::PRIVATE); // error: constant `PRIVATE`\n                                                  //        is private\n```\n\nIn order to fix this error, you need to make the item public by using the `pub`\nkeyword. Example:\n\n```\nmod foo {\n    pub const PRIVATE: u32 = 0x_a_bad_1dea_u32; // We set it public by using the\n                                                // `pub` keyword.\n}\n\nprintln!(\"const value: {}\", foo::PRIVATE); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":168,"byte_end":172,"line_start":6,"line_end":6,"column_start":12,"column_end":16,"is_primary":true,"text":[{"text":"use russh::auth::AuthMethod;","highlight_start":12,"highlight_end":16}],"label":"private module","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the module `auth` is defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\lib.rs","byte_start":4548,"byte_end":4556,"line_start":108,"line_end":108,"column_start":1,"column_end":9,"is_primary":true,"text":[{"text":"mod auth;","highlight_start":1,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0603]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: module `auth` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:6:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse russh::auth::AuthMethod;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate module\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the module `auth` is defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\lib.rs:108:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mmod auth;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module `key` is private","code":{"code":"E0603","explanation":"A private item was used outside its scope.\n\nErroneous code example:\n\n```compile_fail,E0603\nmod foo {\n    const PRIVATE: u32 = 0x_a_bad_1dea_u32; // This const is private, so we\n                                            // can't use it outside of the\n                                            // `foo` module.\n}\n\nprintln!(\"const value: {}\", foo::PRIVATE); // error: constant `PRIVATE`\n                                                  //        is private\n```\n\nIn order to fix this error, you need to make the item public by using the `pub`\nkeyword. Example:\n\n```\nmod foo {\n    pub const PRIVATE: u32 = 0x_a_bad_1dea_u32; // We set it public by using the\n                                                // `pub` keyword.\n}\n\nprintln!(\"const value: {}\", foo::PRIVATE); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1017,"byte_end":1020,"line_start":38,"line_end":38,"column_start":64,"column_end":67,"is_primary":true,"text":[{"text":"    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {","highlight_start":64,"highlight_end":67}],"label":"private module","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1022,"byte_end":1031,"line_start":38,"line_end":38,"column_start":69,"column_end":78,"is_primary":false,"text":[{"text":"    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {","highlight_start":69,"highlight_end":78}],"label":"enum `PublicKey` is not publicly re-exported","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the module `key` is defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\lib.rs","byte_start":4693,"byte_end":4700,"line_start":118,"line_end":118,"column_start":1,"column_end":8,"is_primary":true,"text":[{"text":"mod key;","highlight_start":1,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0603]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: module `key` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:38:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14menum `PublicKey` is not publicly re-exported\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate module\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the module `key` is defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\lib.rs:118:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mmod key;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Write`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":133,"byte_end":138,"line_start":5,"line_end":5,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"use async_std::io::{ReadExt, Write, AsyncWriteExt};","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":131,"byte_end":138,"line_start":5,"line_end":5,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"use async_std::io::{ReadExt, Write, AsyncWriteExt};","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Write`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:5:30\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_std::io::{ReadExt, Write, AsyncWriteExt};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":580,"byte_end":599,"line_start":21,"line_end":21,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":576,"byte_end":602,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated type `FutureBool` not found for `Self`","code":{"code":"E0220","explanation":"The associated type used was not defined in the trait.\n\nErroneous code example:\n\n```compile_fail,E0220\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<F=i32>; // error: associated type `F` not found for `T1`\n\n// or:\n\ntrait T2 {\n    type Bar;\n\n    // error: Baz is used but not declared\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n\nMake sure that you have defined the associated type in the trait body.\nAlso, verify that you used the right trait or you didn't misspell the\nassociated type name. Example:\n\n```\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<Bar=i32>; // ok!\n\n// or:\n\ntrait T2 {\n    type Bar;\n    type Baz; // we declare `Baz` in our trait.\n\n    // and now we can use it here:\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1042,"byte_end":1052,"line_start":38,"line_end":38,"column_start":89,"column_end":99,"is_primary":true,"text":[{"text":"    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {","highlight_start":89,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Self` has the following associated type","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1042,"byte_end":1052,"line_start":38,"line_end":38,"column_start":89,"column_end":99,"is_primary":true,"text":[{"text":"    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {","highlight_start":89,"highlight_end":99}],"label":null,"suggested_replacement":"Error","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0220]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated type `FutureBool` not found for `Self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:38:89\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: `Self` has the following associated type: `Error`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime parameters or bounds on method `check_server_key` do not match the trait declaration","code":{"code":"E0195","explanation":"The lifetime parameters of the method do not match the trait declaration.\n\nErroneous code example:\n\n```compile_fail,E0195\ntrait Trait {\n    fn bar<'a,'b:'a>(x: &'a str, y: &'b str);\n}\n\nstruct Foo;\n\nimpl Trait for Foo {\n    fn bar<'a,'b>(x: &'a str, y: &'b str) {\n    // error: lifetime parameters or bounds on method `bar`\n    // do not match the trait declaration\n    }\n}\n```\n\nThe lifetime constraint `'b` for `bar()` implementation does not match the\ntrait declaration. Ensure lifetime declarations match exactly in both trait\ndeclaration and implementation. Example:\n\n```\ntrait Trait {\n    fn t<'a,'b:'a>(x: &'a str, y: &'b str);\n}\n\nstruct Foo;\n\nimpl Trait for Foo {\n    fn t<'a,'b:'a>(x: &'a str, y: &'b str) { // ok!\n    }\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":977,"byte_end":977,"line_start":38,"line_end":38,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {","highlight_start":24,"highlight_end":24}],"label":"lifetimes do not match method in trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0195]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: lifetime parameters or bounds on method `check_server_key` do not match the trait declaration\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:38:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_server_key(&mut self, _server_public_key: &russh::key::PublicKey) -> Self::FutureBool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mlifetimes do not match method in trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated type `FutureUnit` not found for `Self`","code":{"code":"E0220","explanation":"The associated type used was not defined in the trait.\n\nErroneous code example:\n\n```compile_fail,E0220\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<F=i32>; // error: associated type `F` not found for `T1`\n\n// or:\n\ntrait T2 {\n    type Bar;\n\n    // error: Baz is used but not declared\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n\nMake sure that you have defined the associated type in the trait body.\nAlso, verify that you used the right trait or you didn't misspell the\nassociated type name. Example:\n\n```\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<Bar=i32>; // ok!\n\n// or:\n\ntrait T2 {\n    type Bar;\n    type Baz; // we declare `Baz` in our trait.\n\n    // and now we can use it here:\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1165,"byte_end":1175,"line_start":42,"line_end":42,"column_start":49,"column_end":59,"is_primary":true,"text":[{"text":"    fn channel_open_session(&mut self) -> Self::FutureUnit {","highlight_start":49,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Self` has the following associated type","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":1165,"byte_end":1175,"line_start":42,"line_end":42,"column_start":49,"column_end":59,"is_primary":true,"text":[{"text":"    fn channel_open_session(&mut self) -> Self::FutureUnit {","highlight_start":49,"highlight_end":59}],"label":null,"suggested_replacement":"Error","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0220]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated type `FutureUnit` not found for `Self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:42:49\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn channel_open_session(&mut self) -> Self::FutureUnit {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: `Self` has the following associated type: `Error`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `russh::client::Config` has no field named `connection_timeout`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2121,"byte_end":2139,"line_start":80,"line_end":80,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"        connection_timeout: Some(Duration::from_secs(10)),","highlight_start":9,"highlight_end":27}],"label":"`russh::client::Config` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `client_id`, `limits`, `window_size`, `maximum_packet_size`, `preferred` ... and 4 others","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `russh::client::Config` has no field named `connection_timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:80:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        connection_timeout: Some(Duration::from_secs(10)),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`russh::client::Config` does not have this field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available fields are: `client_id`, `limits`, `window_size`, `maximum_packet_size`, `preferred` ... and 4 others\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2444,"byte_end":2450,"line_start":90,"line_end":90,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"    let mut sess = connect(config, (host, port), SshHandler)","highlight_start":28,"highlight_end":34}],"label":"expected `Arc<Config>`, found `Config`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2436,"byte_end":2443,"line_start":90,"line_end":90,"column_start":20,"column_end":27,"is_primary":false,"text":[{"text":"    let mut sess = connect(config, (host, port), SshHandler)","highlight_start":20,"highlight_end":27}],"label":"arguments to this function are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `Arc<russh::client::Config>`\n   found struct `russh::client::Config`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\client\\mod.rs","byte_start":21425,"byte_end":21432,"line_start":637,"line_end":637,"column_start":14,"column_end":21,"is_primary":true,"text":[{"text":"pub async fn connect<H: Handler + Send + 'static, A: ToSocketAddrs>(","highlight_start":14,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"call `Into::into` on this expression to convert `russh::client::Config` into `Arc<russh::client::Config>`","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2450,"byte_end":2450,"line_start":90,"line_end":90,"column_start":34,"column_end":34,"is_primary":true,"text":[{"text":"    let mut sess = connect(config, (host, port), SshHandler)","highlight_start":34,"highlight_end":34}],"label":null,"suggested_replacement":".into()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:90:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m90\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut sess = connect(config, (host, port), SshHandler)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Arc<Config>`, found `Config`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14marguments to this function are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected struct `\u001b[0m\u001b[0m\u001b[1m\u001b[35mArc<\u001b[0m\u001b[0mrussh::client::Config\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found struct `russh::client::Config`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\rsproxy.cn-e3de039b2554c837\\russh-0.43.0\\src\\client\\mod.rs:637:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m637\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn connect<H: Handler + Send + 'static, A: ToSocketAddrs>(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: call `Into::into` on this expression to convert `russh::client::Config` into `Arc<russh::client::Config>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m90\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    let mut sess = connect(config\u001b[0m\u001b[0m\u001b[38;5;10m.into()\u001b[0m\u001b[0m, (host, port), SshHandler)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `userauth_password` found for struct `russh::client::Handle` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2606,"byte_end":2623,"line_start":95,"line_end":95,"column_start":10,"column_end":27,"is_primary":true,"text":[{"text":"    sess.userauth_password(username, password)","highlight_start":10,"highlight_end":27}],"label":"method not found in `Handle<SshHandler>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `userauth_password` found for struct `russh::client::Handle` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:95:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sess.userauth_password(username, password)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Handle<SshHandler>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 25 previous errors; 2 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 25 previous errors; 2 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0195, E0220, E0252, E0308, E0407, E0432, E0437, E0560, E0599...","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0195, E0220, E0252, E0308, E0407, E0432, E0437, E0560, E0599...\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0195`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0195`.\u001b[0m\n"}
