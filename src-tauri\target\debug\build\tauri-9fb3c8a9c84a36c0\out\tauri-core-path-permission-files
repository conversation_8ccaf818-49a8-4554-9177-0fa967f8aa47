["\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-9fb3c8a9c84a36c0\\out\\permissions\\path\\autogenerated\\default.toml"]