{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 17069650581314812224, "deps": [[40386456601120721, "percent_encoding", false, 10312266100560858564], [442785307232013896, "tauri_runtime", false, 6978390629795326186], [1200537532907108615, "url<PERSON><PERSON>n", false, 6512860506318385237], [3150220818285335163, "url", false, 5603857225676842057], [4143744114649553716, "raw_window_handle", false, 7517658832036738009], [4341921533227644514, "muda", false, 7648860812156589693], [4919829919303820331, "serialize_to_javascript", false, 6710817874931979593], [5986029879202738730, "log", false, 7236839104650837312], [7752760652095876438, "tauri_runtime_wry", false, 10338378284588855201], [8539587424388551196, "webview2_com", false, 13234043157692173674], [9010263965687315507, "http", false, 13014744829488832296], [9228235415475680086, "tauri_macros", false, 18176082035131847846], [9538054652646069845, "tokio", false, 1783695341924160198], [9689903380558560274, "serde", false, 6121258471593067390], [9920160576179037441, "getrandom", false, 8822169597172634618], [10229185211513642314, "mime", false, 16994023679160200463], [10629569228670356391, "futures_util", false, 9468442078283782137], [10755362358622467486, "build_script_build", false, 10153625634090657252], [10806645703491011684, "thiserror", false, 17452964001142711969], [11050281405049894993, "tauri_utils", false, 7084657927408820804], [11989259058781683633, "dunce", false, 15514511650017520897], [12565293087094287914, "window_vibrancy", false, 10850676740201089860], [12986574360607194341, "serde_repr", false, 13703274093119033485], [13077543566650298139, "heck", false, 17901251952272125154], [13625485746686963219, "anyhow", false, 3520052488611987028], [14585479307175734061, "windows", false, 13425054998928322741], [15367738274754116744, "serde_json", false, 9722110262108598114], [16928111194414003569, "dirs", false, 8883364103899104014], [17155886227862585100, "glob", false, 271191148063710908]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-232c705fd279ecd9\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}