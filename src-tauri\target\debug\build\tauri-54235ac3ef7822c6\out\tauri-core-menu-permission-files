["\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\workspace\\rust\\sftp-web\\src-tauri\\target\\debug\\build\\tauri-54235ac3ef7822c6\\out\\permissions\\menu\\autogenerated\\default.toml"]