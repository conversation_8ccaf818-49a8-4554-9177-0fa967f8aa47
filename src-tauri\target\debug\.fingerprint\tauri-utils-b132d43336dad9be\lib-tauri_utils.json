{"rustc": 10895048813736897673, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 13667997004821700218, "deps": [[561782849581144631, "html5ever", false, 1132142179013753782], [1200537532907108615, "url<PERSON><PERSON>n", false, 2589114494136024871], [3060637413840920116, "proc_macro2", false, 9898791222778704133], [3150220818285335163, "url", false, 12655493810620714798], [3191507132440681679, "serde_untagged", false, 1685846281213186435], [4899080583175475170, "semver", false, 8371616595847207021], [5578504951057029730, "serde_with", false, 3760849744018037698], [5986029879202738730, "log", false, 13388491360176584128], [6262254372177975231, "kuchiki", false, 16907348538929110972], [6606131838865521726, "ctor", false, 14182983749263760031], [6913375703034175521, "schemars", false, 10232491167744959992], [7170110829644101142, "json_patch", false, 14462239644424083236], [8319709847752024821, "uuid", false, 11339950564336125742], [9010263965687315507, "http", false, 13707547862268365133], [9451456094439810778, "regex", false, 7881417031046486631], [9689903380558560274, "serde", false, 6763933132116834322], [10806645703491011684, "thiserror", false, 6103424829622831816], [11655476559277113544, "cargo_metadata", false, 15031848965720550747], [11989259058781683633, "dunce", false, 8993868349159878499], [13625485746686963219, "anyhow", false, 17145296041573005949], [14132538657330703225, "brotli", false, 13442052967841458234], [15367738274754116744, "serde_json", false, 11459032808059228803], [15609422047640926750, "toml", false, 12405456715105559855], [15622660310229662834, "walkdir", false, 13637727522899890893], [15932120279885307830, "memchr", false, 261397065016885617], [17146114186171651583, "infer", false, 18018647110904739410], [17155886227862585100, "glob", false, 9625159244041247292], [17186037756130803222, "phf", false, 3672492239073589384], [17990358020177143287, "quote", false, 13527854320717894611]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-b132d43336dad9be\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}