# 连接后目录为空问题诊断

## 问题描述
连接到 SFTP 服务器后，文件浏览器显示目录为空，但实际上服务器上可能有文件。

## 可能的原因

### 1. 权限问题
- SFTP 用户没有读取目录的权限
- 用户被限制在特定的 chroot 目录中

### 2. 路径问题
- 连接后的默认目录可能不是预期的目录
- 服务器配置的用户主目录为空

### 3. 编码问题
- 文件名使用非 UTF-8 编码
- 特殊字符导致文件名解析失败

### 4. 连接问题
- SFTP 会话不稳定
- 网络延迟或超时

## 新增的诊断功能

### 1. 详细的后端日志
现在后端会输出详细的调试信息：

```
=== 开始列出目录 ===
请求路径: '/' (连接ID: conn_xxx)
当前活跃连接数: 1
会话已找到，创建 SFTP 连接...
标准化路径: '/' -> '/'
当前工作目录: /home/<USER>
尝试读取目录: '/'
原始条目数量: 5
处理条目 1: /home/<USER>/file1.txt
  文件名: 'file1.txt'
  -> 添加文件: file1.txt (路径: /home/<USER>/file1.txt, 目录: false, 大小: 1024)
=== 目录读取完成 ===
最终文件数量: 5 (原始: 5)
```

### 2. 连接诊断按钮
在文件浏览器中添加了"诊断"按钮，可以：
- 检查连接状态
- 获取当前工作目录
- 验证 SFTP 会话

### 3. 改进的空目录显示
当目录为空时，界面会显示：
- 可能的原因列表
- 诊断连接按钮
- 更友好的提示信息

## 诊断步骤

### 步骤 1: 检查连接状态
1. 连接到 SFTP 服务器
2. 如果目录为空，点击"诊断"按钮
3. 查看诊断结果和当前工作目录

### 步骤 2: 查看后端日志
1. 打开浏览器开发者工具
2. 查看控制台输出
3. 检查后端日志中的详细信息

### 步骤 3: 验证服务器配置
1. 使用其他 SFTP 客户端连接同一服务器
2. 检查用户权限和主目录设置
3. 确认文件确实存在

### 步骤 4: 测试不同路径
1. 尝试手动导航到已知存在文件的目录
2. 使用面包屑导航测试不同路径
3. 检查路径格式是否正确

## 常见解决方案

### 1. 权限问题
```bash
# 在服务器上检查权限
ls -la /path/to/directory
chmod 755 /path/to/directory  # 如果需要
```

### 2. 路径问题
- 尝试导航到 `/home/<USER>
- 检查服务器的 SFTP 配置中的默认目录设置

### 3. 编码问题
- 确保服务器使用 UTF-8 编码
- 检查文件名是否包含特殊字符

### 4. 连接问题
- 重新连接
- 检查网络连接稳定性
- 增加连接超时时间

## 调试输出示例

### 正常情况
```
=== 开始列出目录 ===
请求路径: '/' (连接ID: conn_1234567890)
当前活跃连接数: 1
会话已找到，创建 SFTP 连接...
当前工作目录: /home/<USER>
原始条目数量: 3
最终文件数量: 3 (原始: 3)
```

### 权限问题
```
读取目录失败 [/]: Permission denied
```

### 路径问题
```
读取目录失败 [/nonexistent]: No such file or directory
```

### 编码问题
```
处理条目 1: /home/<USER>/文件名乱码
  警告: 无法获取文件名，跳过
最终文件数量: 0 (原始: 1)
```

## 下一步行动

1. **启动应用并连接**
2. **查看详细日志** - 检查控制台输出
3. **使用诊断功能** - 点击诊断按钮
4. **报告具体错误** - 提供详细的日志信息

现在的应用提供了丰富的调试信息，应该能够快速定位目录为空的具体原因。
