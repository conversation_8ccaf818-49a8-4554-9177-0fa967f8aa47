cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\workspace\rust\sftp-web\src-tauri\target\debug\build\tauri-plugin-opener-d39f14847c145f30\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=D:\workspace\rust\sftp-web\src-tauri\target\debug\build\tauri-plugin-opener-d39f14847c145f30\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-opener-2.3.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
